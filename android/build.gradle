group 'com.baidu.mapapi.base'
version '1.0'

buildscript {
    repositories {
        mavenCentral()
//        maven { url 'https://maven.aliyun.com/repository/central' }
//        maven { url 'https://maven.aliyun.com/repository/jcenter' }
//        maven { url 'https://maven.aliyun.com/repository/google' }
//        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
//        maven { url 'https://maven.aliyun.com/repository/public' }
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.4.1'
    }
}

rootProject.allprojects {
    repositories {
        mavenCentral()
//        maven { url 'https://maven.aliyun.com/repository/central' }
//        maven { url 'https://maven.aliyun.com/repository/jcenter' }
//        maven { url 'https://maven.aliyun.com/repository/google' }
//        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
//        maven { url 'https://maven.aliyun.com/repository/public' }
        google()
        jcenter()
        mavenLocal()
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 29

    defaultConfig {
        minSdkVersion 19
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            // 设置支持的SO库架构（开发者可以根据需要，选择一个或多个平台的so）
            abiFilters "armeabi-v7a", "arm64-v8a", "x86","x86_64"
        }
    }
    lintOptions {
        disable 'InvalidPackage'
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['jniLibs']
        }
    }
}

dependencies {
//    api fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'com.baidu.lbsyun:BaiduMapSDK_Map:7.5.0'
    implementation 'com.baidu.lbsyun:BaiduMapSDK_Search:7.5.0'
    implementation 'com.baidu.lbsyun:BaiduMapSDK_Util:7.5.0'
    implementation 'com.baidu.lbsyun:BaiduMapSDK_Location_All:9.1.8'
}
