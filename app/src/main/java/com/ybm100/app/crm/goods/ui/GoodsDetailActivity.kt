package com.ybm100.app.crm.goods.ui

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.text.TextUtils
import android.text.style.ImageSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.fold.recyclyerview.BaseQuickAdapter
import com.fold.recyclyerview.BaseViewHolder
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.ms.banner.BannerConfig
import com.xyy.common.navigationbar.AbsNavigationBar
import com.xyy.common.navigationbar.DefaultNavigationBar
import com.xyy.common.util.*
import com.xyy.common.widget.flowtag.adaper.BaseFlowAdapter
import com.xyy.common.widget.flowtag.adaper.BaseTagHolder
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity
import com.xyy.utilslibrary.utils.DisplayUtils
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.goods.adapter.GoodsZoneListAdapter
import com.ybm100.app.crm.goods.bean.*
import com.ybm100.app.crm.goods.presenter.GoodsDetailPresenter
import com.ybm100.app.crm.task.activity.SelectCustomersActivity
import com.ybm100.app.crm.utils.ShareHelper
import com.ybm100.app.crm.utils.SnowGroundUtils
import com.ybm100.app.crm.widget.CenterAlignImageSpan
import com.ybm100.app.crm.widget.CustomBottomSheetDialog
import com.ybm100.app.crm.widget.ShareGoodsDialog
import com.ybm100.app.crm.widget.WrapLayoutManager
import com.ybmmarket20.xyyreport.session.SessionManager
import com.zhy.view.flowlayout.FlowLayout
import com.zhy.view.flowlayout.TagAdapter
import kotlinx.android.synthetic.main.activity_goods_detail.*
import com.quick.qt.analytics.QtTrackAgent

class GoodsDetailActivity : BaseMVPCompatActivity<GoodsDetailPresenter>() {

    companion object {
        private const val INTENT_KEY_ID = "intent_key_id"
        private const val INTENT_KEY_TASK_ID = "intent_key_task_id"
        private const val INTENT_KEY_MERCHANT_ID = "intent_key_merchant_id"
        private const val INTENT_KEY_BRANCH_CODE = "intent_key_branch_code"
        private var spmE: String? = null
        private var scmE: String? = null

        fun start(context: Context?, id: String?, taskId: String? = null, branchCode: String? = null, merchantId: Long? = null) {
            context?.run {
                if (id.isNullOrBlank()) {
                    ToastUtils.showShort("商品id错误")
                }
                if (branchCode.isNullOrEmpty() && merchantId == null) {
                    ToastUtils.showShort("branchCode和merchantId不能同时为空")
                }
                val intent = Intent(this, GoodsDetailActivity::class.java).also {
                    it.putExtra(INTENT_KEY_ID, id)
                    it.putExtra(INTENT_KEY_TASK_ID, taskId)
                    it.putExtra(INTENT_KEY_MERCHANT_ID, merchantId)
                    it.putExtra(INTENT_KEY_BRANCH_CODE, branchCode)
                }
                startActivity(intent)
            }
        }
    }


    private var goodsId: Int = 0
    private var goodsName: String = ""
    private var branchCode: String? = ""
    var taskId: String? = ""
    var merchantId: Long? = null
    var goodsDetail: GoodsDetail? = null
    private var selectAllTagList: ArrayList<TagBean> = ArrayList()

    private var descSheetDialog: CustomBottomSheetDialog? = null

    private var shareType: ShareHelper.Companion.SocialMedia? = null

    private var thisMonthGoodsFlowStatistics: GoodsFlowStatistics? = null
    private var lastMonthGoodsFlowStatistics: GoodsFlowStatistics? = null


    override fun initPresenter(): BasePresenter<*, *> {
        return GoodsDetailPresenter()
    }

    override fun showNetError() {
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_goods_detail
    }


    override fun initHead(): AbsNavigationBar<*> {
        return DefaultNavigationBar.Builder(this)
                .setTitle("商品详情")
                .setLeftIcon(R.drawable.nav_return)
                .setRightIcon(R.drawable.platform_sharelogo)
                .setRightIcon(R.drawable.ic_share)
                .setRightClickListener {
                    showSharePopup()

                    trackAdSubModuleClick();

                    if (taskId.isNullOrEmpty()) {
                        /*if (merchantId != null) {
                            //客户详情 -- 商品管理
                        } else {
                            //发现 -- 商品管理
                        }*/
                        UserBehaviorTrackingUtils.track("mc-productmgt-productdetail-recommend")
                    } else {
                        UserBehaviorTrackingUtils.track(Constants.Task.MC_PRODUCT_DETAIL_RECOMMEND)
                    }
                }.builder()
    }


    override fun initTransferData() {
        super.initTransferData()
        try {

            intent.data?.let {
                intent.putExtra(INTENT_KEY_ID, it.getQueryParameter("id"))
                intent.putExtra(INTENT_KEY_MERCHANT_ID, it.getQueryParameter("merchantId")?.toLongOrNull())
                intent.putExtra(INTENT_KEY_TASK_ID, it.getQueryParameter("taskId"))
                intent.putExtra(INTENT_KEY_BRANCH_CODE, it.getQueryParameter("branchCode"))
            }
        } catch (e: Exception) {

        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        var goodsIdStr: Int? = null
        try {
            goodsIdStr = intent.getStringExtra(INTENT_KEY_ID)?.toIntOrNull()
            taskId = intent.getStringExtra(INTENT_KEY_TASK_ID)
            merchantId = intent.getLongExtra(INTENT_KEY_MERCHANT_ID, -1)
            branchCode = intent.getStringExtra(INTENT_KEY_BRANCH_CODE) ?: ""

            spmE = SessionManager.get().getPageSession();
            scmE = SessionManager.get().getPageSession();

        } catch (e: Exception) {

        }
        if (merchantId == -1L) {
            merchantId = null
        }

        if (branchCode.isNullOrEmpty()) {
            try {
                SnowGroundUtils.track("goods_detail_error", hashMapOf(
                        "skuId" to (goodsIdStr?.toString() ?: ""),
                        "merchantId" to merchantId.toString(),
                        "taskId" to (taskId ?: "")
                ))
            } catch (e: Exception) {

            }
            if (branchCode.isNullOrEmpty() && merchantId == null) {
                ToastUtils.showShort("branchCode和merchantId不能同时为空")
                finish()
                return
            }
        }

        if (goodsIdStr == null) {
            showToast("商品id异常")
            finish()
            return
        } else {
            goodsId = goodsIdStr
        }
        if (taskId.isNullOrEmpty()) {
            taskId = null
        }
        resetNumIndicator()
        layout_status_view.setOnRetryListener {
            requestGoodsDetail()
        }
        requestGoodsDetail()
        setSellPointView()
        initRecentPurchaseRecords()
        initAVGPriceMonth()
    }


    private fun initRecentPurchaseRecords() {
        if (merchantId == null || merchantId == -1L) {
            // 我的页面进入
            group_purchase_record.visibility = View.GONE
        } else {
            // 客户下进入
            group_purchase_record.visibility = View.VISIBLE
            requestRecentPurchaseRecords()
        }
    }

    private fun requestRecentPurchaseRecords() {
        mPresenter.getRecentPurchaseRecords(merchantId.toString(), goodsId.toString())
    }

    fun requestRecentPurchaseRecordsSuccess(purchaseRecords: List<PurchaseRecord?>) {
        prv_purchase_record_container.setData(purchaseRecords)
    }

    fun requestRecentPurchaseRecordsFailed() {
        prv_purchase_record_container.setData(null)
    }

    private fun resetNumIndicator() {
        val indicatorView = findViewById<TextView>(R.id.numIndicator)
        indicatorView.setBackgroundResource(R.drawable.shape_goods_num_indicator)
        val layoutParams = indicatorView.layoutParams
        layoutParams.width = ConvertUtils.dp2px(44f)
        layoutParams.height = ConvertUtils.dp2px(23f)
        indicatorView.layoutParams = layoutParams
    }

    private fun requestGoodsDetail() {
        mPresenter.requestGoodsDetail(goodsId, merchantId, branchCode)
    }

    private fun setSellPointView() {
        tv_add_sellPoint.setOnClickListener {
            UserBehaviorTrackingUtils.track("mc-productmgt-productdetail-hint")
            if (branchCode.isNullOrEmpty()) {
                ToastUtils.showShort("branchCode为空")
                return@setOnClickListener
            }
            AddSellPointActivity.start(this, goodsId.toString(), goodsDetail?.sellingPoint, convertLabelsToTags(goodsDetail?.labels), branchCode)

        }
        tv_goods_collect.setOnClickListener {
            if (branchCode.isNullOrEmpty()) {
                ToastUtils.showShort("branchCode为空")
                return@setOnClickListener
            }
            val type = if (tv_goods_collect.isActivated) 0 else 1
            mPresenter.collectGood(goodsId.toString(), branchCode ?: "", type, goodsDetail?.showName
                    ?: "")
            if (type == 1) {
                UserBehaviorTrackingUtils.track("mc-productmgt-productdetail-unmark")
            } else {
                UserBehaviorTrackingUtils.track("mc-productmgt-productdetail-mark")
            }

        }
    }

    private fun convertLabelsToTags(labels: List<Labels>?): ArrayList<TagBean>? {
        val result = arrayListOf<TagBean>()
        labels?.forEach {
            result.add(TagBean(it.id?.toIntOrNull() ?: 0, it.label))
        }
        return result
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == AddSellPointActivity.REQUEST_CODE && resultCode == Activity.RESULT_OK) {
            requestGoodsDetail()
        }
    }

    private fun showSharePopup() {
        val dialog = ShareGoodsDialog(this).also { shareDialog ->
            shareDialog.onClickCallBack = {
                shareType = null
                when (it) {
                    1 -> {
                        UserBehaviorTrackingUtils.track(Constants.Task.MC_TASK_PRODUCT_RECOMMEND_TYPE2)
                        shareType = ShareHelper.Companion.SocialMedia.PLATFORM_WECHAT
                        requestShareConfirm()
                    }
                    2 -> {
                        UserBehaviorTrackingUtils.track(Constants.Task.MC_TASK_PRODUCT_RECOMMEND_TYPE3)

                        shareType = ShareHelper.Companion.SocialMedia.PLATFORM_WECHAT_CIRCLE
                        requestShareConfirm()
                        trackAdSubModuleClickWithShareView("2")
                    }
                    3 -> {
                        UserBehaviorTrackingUtils.track(Constants.Task.MC_TASK_PRODUCT_RECOMMEND_TYPE1)

                        //跳转选择客户
                        SelectCustomersActivity.startActivity(this, taskId, goodsId.toString(), Constants.Task.CONSTANT_FROM_GOODS_DETAIL,
                                false, branchCode)
                        trackAdSubModuleClickWithShareView("3")
                    }
                }
            }
        }
        dialog.show()
    }

    private fun requestShareConfirm() {
        mPresenter.requestShareConfirm(goodsId, taskId ?: "")
    }

    fun requestShareConfirmSuccess(confirm: ShareConfirm) {
        if (shareType != null) {
            ShareHelper.shareUrl(this, shareType!!, confirm.shareUrl, confirm.appName,
                    confirm.content, confirm.appLogo, object : ShareHelper.ShareCallback {
                override fun onCancel(platform: String?) {
                }

                override fun onCompleted(platform: String?) {
                }

                override fun onError(platform: String?, errMsg: String?) {
                }

                override fun onStart(platform: String?) {
                }

            })
        }
    }

    fun requestCollectGoodSuccess() {
        if (this.goodsDetail != null) {
            tv_goods_collect.isActivated = !tv_goods_collect.isActivated
            tv_goods_collect.text = if (tv_goods_collect.isActivated) "已收藏" else "收藏"
            this.goodsDetail?.collect = if (tv_goods_collect.isActivated) 1 else 0
            updateSellPointView(this.goodsDetail!!)
        } else {
            requestGoodsDetail()
        }
    }

    fun requestGoodDetailSuccess(goodsDetail: GoodsDetail?) {
        if (goodsDetail == null) {
            requestGoodDetailFail("")
            return
        }
        prv_purchase_record_container.setProductUnit(goodsDetail.productUnit ?: "")
        if (this.branchCode.isNullOrEmpty()) {
            this.branchCode = goodsDetail.branchCode
        }
        layout_status_view.showContent()
        this.goodsDetail = goodsDetail
        updateBanner(goodsDetail.imagesList)
        updateBaseInfo(goodsDetail)
        updateService(goodsDetail.promiseList)
        updatePromotion(goodsDetail.productPromoList)
        initGoodsFlowStatistics()
        updateSellPointView(goodsDetail)
        updateSellPointData(goodsDetail)
        updateZoneList(goodsDetail.zoneList)
        updateShopInfo(goodsDetail.shopName)
//        updateDiscountPrice(goodsDetail)
        if (merchantId != null && !branchCode.isNullOrEmpty()) {
            mPresenter.getDiscountPrice(merchantId.toString(), branchCode!!, goodsId.toString())
        }
    }

    private fun updateShopInfo(shopName: String?) {
        // 更新店铺名
        val groupShop = findViewById<Group?>(R.id.group_shop)
        val showNameView = findViewById<TextView?>(R.id.tv_shop_name)
        if (shopName.isNullOrEmpty()) {
            groupShop?.visibility = View.GONE
        } else {
            groupShop?.visibility = View.VISIBLE
            showNameView?.text = shopName
        }
    }

    private fun updateZoneList(zoneList: List<String?>?) {
        val zoneListView = findViewById<RecyclerView>(R.id.rv_zone_list)
        val zoneLabelView = findViewById<TextView>(R.id.tv_goods_zone_label)
        if (zoneList == null || zoneList.isEmpty()) {
            zoneListView.visibility = View.GONE
            zoneLabelView.visibility = View.GONE
        } else {
            zoneListView.visibility = View.VISIBLE
            zoneLabelView.visibility = View.VISIBLE
            zoneListView.layoutManager = FlexboxLayoutManager(this)
            zoneListView.adapter = GoodsZoneListAdapter(zoneList)
        }
    }


    fun requestDiscountPriceSuccess(discountPrice: String?) {
        if (merchantId == null
                || goodsDetail == null
                || discountPrice.isNullOrEmpty()) {
            tv_discount_price.visibility = View.GONE
        } else {
            tv_discount_price.visibility = View.VISIBLE
            tv_discount_price.text = discountPrice
            tv_discount_price.setOnClickListener {
                showDiscountPriceDesc(goodsDetail?.discountPriceText)
            }
        }
    }


    private fun showDiscountPriceDesc(discountPriceText: DiscountPriceText?) {
        if (discountPriceText == null) {
            ToastUtils.showShortSafe("到手价计算说明为空")
            return
        }
        if (descSheetDialog == null) {
            val dp380 = ConvertUtils.dp2px(600F)
            descSheetDialog = CustomBottomSheetDialog(this, dp380, dp380).also {
                it.setContentView(R.layout.layout_discount_price_desc)
                it.findViewById<TextView?>(R.id.tv_sub_title)?.let { title ->
                    title.visibility = if (discountPriceText.title.isNullOrBlank()) {
                        View.GONE
                    } else {
                        title.text = discountPriceText.title
                        View.VISIBLE
                    }
                }
                it.findViewById<TextView?>(R.id.tv_content)?.let { content ->
                    content.visibility = if (discountPriceText.content.isNullOrBlank()) {
                        View.GONE
                    } else {
                        content.text = discountPriceText.content
                        View.VISIBLE
                    }
                }
                discountPriceText.discountPriceTextList?.forEach { itemData ->
                    if (itemData != null) {
                        val container = it.findViewById<LinearLayout>(R.id.ll_container)
                        generateAndInsertItemView(container, itemData)
                    }
                }
                val closeClickListener = View.OnClickListener {
                    descSheetDialog?.hide()
                }
                it.findViewById<View>(R.id.iv_close)?.setOnClickListener(closeClickListener)
                it.findViewById<View>(R.id.tv_confirm)?.setOnClickListener(closeClickListener)
            }
        }
        descSheetDialog?.show()
    }

    private fun generateAndInsertItemView(parent: ViewGroup?, itemData: DiscountPriceTextList) {
        if (parent == null) return
        val itemView = layoutInflater.inflate(R.layout.item_discount_price_desc, parent, false)
        if (itemView is ViewGroup) {
            itemView.findViewById<TextView?>(R.id.tv_title)?.let { content ->
                content.visibility = if (itemData.title.isNullOrBlank()) {
                    View.GONE
                } else {
                    content.text = itemData.title
                    View.VISIBLE
                }
            }
            itemView.findViewById<TextView?>(R.id.tv_content)?.let { content ->
                content.visibility = if (itemData.content.isNullOrBlank()) {
                    View.GONE
                } else {
                    content.text = itemData.content
                    View.VISIBLE
                }
            }
        }
        parent.addView(itemView)
    }

    private fun updateSellPointView(goodsDetail: GoodsDetail) {
        //一句话卖点
        if (TextUtils.isEmpty(goodsDetail.content)) {
            group_word.visibility = View.GONE
        } else {
            group_word.visibility = View.VISIBLE
        }
        //自定义卖点 已收藏展示自定义卖点
        if (1 == goodsDetail.collect) {
            group_custom_sellPoint.visibility = View.VISIBLE
            //如该商品无自定义卖点，仅展示编辑自定义卖点入
            if (TextUtils.isEmpty(goodsDetail.sellingPoint)) {
                tv_custom_context.visibility = View.GONE
            } else {
                tv_custom_context.visibility = View.VISIBLE
            }
            //卖点标签
        } else {
            group_custom_sellPoint.visibility = View.GONE
            tv_custom_context.visibility = View.GONE
        }
        if (TextUtils.isEmpty(goodsDetail.content) && 1 != goodsDetail.collect) {
            group_sellPoint.visibility = View.GONE
        } else {
            group_sellPoint.visibility = View.VISIBLE
        }
    }

    private fun updateSellPointData(goodsDetail: GoodsDetail) {
        tv_goods_collect.isActivated = goodsDetail.collect == 1
        tv_goods_collect.text = if (goodsDetail.collect == 1) "已收藏" else "收藏"
        tv_sellPoint_content.text = goodsDetail.content
        if (!goodsDetail.sellingPoint.isNullOrEmpty()) {
            tv_custom_context.text = goodsDetail.sellingPoint
        }
        tfl_sellPoint_tags.adapter = object : TagAdapter<Labels?>(goodsDetail.labels) {
            override fun getView(parent: FlowLayout, position: Int, labels: Labels?): View {
                val inflate = View.inflate(this@GoodsDetailActivity, R.layout.item_tag_sell_point_detail, null)
                val viewById = inflate.findViewById<TextView>(R.id.tv_tagName)
                viewById.text = labels?.label
                val lp = ViewGroup.MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT)
                lp.topMargin = DisplayUtils.dp2px(10f)
                lp.rightMargin = DisplayUtils.dp2px(10f)
                viewById.layoutParams = lp
                return inflate
            }
        }
    }

    private fun updateService(promiseList: List<Promise>?) {
        promiseList?.let {
            rv_service_value.layoutManager = FlexboxLayoutManager(this@GoodsDetailActivity)
            rv_service_value.adapter = GoodsDetailPromiseAdapter(it)
        }
    }

    private fun initGoodsFlowStatistics() {
        rb_this_month.isChecked = true
        rb_this_month.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                updateSalesDataInternal(thisMonthGoodsFlowStatistics)
                requestGoodsFlowStatistics(true)
            }
        }
        rb_last_month.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                updateSalesDataInternal(lastMonthGoodsFlowStatistics)
                requestGoodsFlowStatistics(false)
            }
        }
        updateSalesDataInternal(null)
        // 优先本月
        requestGoodsFlowStatistics(rb_this_month.isChecked)
    }

    private fun requestGoodsFlowStatistics(isThisMonth: Boolean) {
        mPresenter.getGoodsFlow(isThisMonth, goodsId.toString())
    }

    fun requestGoodsFlowStatisticsResult(isThisMonth: Boolean, goodsFlowStatistics: GoodsFlowStatistics?) {
        if (isThisMonth) {
            thisMonthGoodsFlowStatistics = goodsFlowStatistics
        } else {
            lastMonthGoodsFlowStatistics = goodsFlowStatistics
        }
        updateSalesDataInternal(goodsFlowStatistics)
    }

    private fun updateSalesDataInternal(goodsFlowStatistics: GoodsFlowStatistics?) {
        val purchasedCount = goodsFlowStatistics?.buyCustomerNum ?: "--"
        val unPurchasedCount = goodsFlowStatistics?.noBuyCustomerNum ?: "--"
        tv_purchased.text = "已购客户数：${purchasedCount}家"
        tv_un_purchased.text = "未购客户数：${unPurchasedCount}家"
        tv_month_purchase_price.text = goodsFlowStatistics?.payGmv ?: "--"
        tv_month_purchase_num.text = goodsFlowStatistics?.saleNum ?: "--"
        tv_month_purchase_num_unit.text = goodsDetail?.productUnit ?: ""
        tv_purchased.setOnClickListener {
            val url = "/goods_already_buy_page?skuId=${goodsId}&type=${if (rb_this_month.isChecked) 3 else 8}"
            ContainerRuntime.getFlutterRouter().open(this@GoodsDetailActivity, url)
        }
        tv_un_purchased.setOnClickListener {
            val url = "/goods_no_buy_page?skuId=${goodsId}&type=${if (rb_this_month.isChecked) 3 else 8}"
            ContainerRuntime.getFlutterRouter().open(this@GoodsDetailActivity, url)
        }
    }

    private fun updatePromotion(promoList: List<Promotion?>?) {
        if (!promoList.isNullOrEmpty()) {
            group_discountsInfo.visibility = View.VISIBLE
            tag_discountsInfo.text = promoList[0]?.promoTypeStr
            tv_discountsInfo.text = promoList[0]?.planDescription
            if (promoList.size > 1) {
                group_discountsInfo1.visibility = View.VISIBLE
                tag_discountsInfo1.text = promoList[1]?.promoTypeStr
                tv_discountsInfo1.text = promoList[1]?.planDescription
            } else {
                group_discountsInfo1.visibility = View.GONE
            }
        } else {
            group_discountsInfo.visibility = View.GONE
        }
        cl_discountsInfo.setOnClickListener {
            showMoreDiscountsInfo(promoList)

            if (taskId.isNullOrEmpty()) {
                UserBehaviorTrackingUtils.track("mc-productmgt-productdetail-promotion")
            } else {
                UserBehaviorTrackingUtils.track(Constants.Task.MC_PRODUCT_DETAIL_PROMOTION)
            }
        }
    }

    /**
     * 展示更多的优惠
     */
    private fun showMoreDiscountsInfo(promoList: List<Promotion?>?) {
        val bottomSheetDialog = BottomSheetDialog(this)
        val view: View = LayoutInflater.from(this).inflate(R.layout.discounts_more_layout, null, false)
        bottomSheetDialog.setContentView(view)
        bottomSheetDialog.delegate.findViewById<View>(R.id.design_bottom_sheet)?.setBackgroundColor(Abase.getResources().getColor(android.R.color.transparent))
        view.findViewById<ImageView>(R.id.iv_close).setOnClickListener { bottomSheetDialog.dismiss() }
        val mRecyclerView = view.findViewById<RecyclerView>(R.id.recycler_view)
        val adAdapter = object : BaseQuickAdapter<Promotion?, BaseViewHolder?>(R.layout.item_discounts_layout) {
            override fun convert(helper: BaseViewHolder, item: Promotion?) {
                if (helper == null || item == null) return
                if (merchantId == null) {
                    helper.setVisible(R.id.iv_arrow, true)
                } else {
                    helper.setVisible(R.id.iv_arrow, false)
                }
                helper.setText(R.id.tv_discountsInfo, item.planDescription)
                helper.setText(R.id.tag_discountsInfo, item.promoTypeStr)
            }
        }
        mRecyclerView.adapter = adAdapter
        mRecyclerView.layoutManager = WrapLayoutManager(this)
        adAdapter.setOnItemClickListener { _, _, position ->
            if (merchantId == null) {
                PromotionCustomListActivity.newInstance(this, goodsId.toString(),
                        adAdapter.getItem(position)?.promoId ?: "")
            }
            bottomSheetDialog.dismiss()
        }
        adAdapter.setNewData(promoList)
        bottomSheetDialog.show()
    }

    private fun updateBaseInfo(goodsDetail: GoodsDetail?) {
        goodsDetail?.run {
            //金额
            tv_goods_price.text = run {
                val fobFormat = MathUtils.getFormatNum(fob ?: 0.00)
                StringUtils.handleString(fobFormat, fobFormat.indexOf("."), 0.66f)
            }
            //建议零售价、终端毛利率
            tv_goods_tips.text = run {
                "建议零售价 ¥${suggestPrice
                        ?: "0.00"}（终端毛利率${grossMargin
                        ?: "--"}）"
            }
            goodsName = showName ?: ""
            //商品标签,商品名
            tv_goods_name.text = run {
                if (isThirdCompany == 0) {
                    addIcon(showName)
                } else {
                    showName
                }
            }
            //规格、库存
            tv_goods_simple_info.text = run {
                "规格：${spec ?: ""}  库存：${stockAvailableQtyStr ?: ""}"
            }
            //有效期
            tv_expired_value.text = "近至${nearEffect ?: "--"} 远至${farEffect ?: "--"}"
            //中包装
            tv_package_medium_value.text = mediumPackage ?: ""
            //件包装
            tv_package_value.text = (pieceLoading ?: "") + (productUnit ?: "")
            //生产厂家
            tv_manufactor_value.text = manufacturer ?: ""
            //批准文号
            tv_no_value.text = approvalNumber ?: ""
            //保质期
            tv_EXP_value.text = shelfLife ?: ""
        }

    }

    private fun updateBanner(imagesList: List<String?>?) {
        banner.updateBannerStyle(BannerConfig.NUM_INDICATOR)
        banner.setPages(imagesList ?: emptyList<String>(), CustomViewHolder()).setAutoPlay(true)
                .setDelayTime(3000)
                .start()
    }

    /**
     * 药店名称添加图标
     */
    private fun addIcon(text: String?): SpannableString {
        val ss = SpannableString("  $text")
        val d = ContextCompat.getDrawable(this, R.drawable.icon_goods_tip)
        d!!.setBounds(0, 0, d.intrinsicWidth, d.intrinsicHeight)
        //构建ImageSpan
        val span = CenterAlignImageSpan(d)
        ss.setSpan(span, 0, 1, ImageSpan.ALIGN_BASELINE)
        return ss
    }

    internal class GoodsDetailPromiseAdapter(data: List<Promise>?) : BaseFlowAdapter<Promise, BaseTagHolder>(R.layout.item_promise_tag, data) {
        override fun convert(tagHelper: BaseTagHolder?, item: Promise?) {
            val text = tagHelper?.getView<TextView>(R.id.tv_tagName)
            text?.text = item?.title
        }
    }

    fun requestGoodDetailFail(msg: String?) {
        layout_status_view.showError(msg)
    }

    fun requestShareConfirmFail(msg: String?) {
        ToastUtils.showShort(msg ?: "请求失败")
    }


    private fun initAVGPriceMonth() {
        mPresenter.getAVGPrice(goodsId.toString(), branchCode ?: "", merchantId)
    }

    fun requestAVGPriceMonthSuccess(data: AVGPriceMonth?) {
        apmv_avg_price_container.setData(data)
    }

    fun requestAVGPriceMonthFailed() {
        apmv_avg_price_container.setData(null)
    }

    fun trackAdSubModuleClick(){
        var mAdSpmStr = "4_1." + goodsId + ".prodDetHeader@1." + "btn@2." + scmE;
        var mAdScmStr = "appFE." + "0." + "all_0." + "text-分享." + scmE;

        val map = HashMap<String, Any>()
        map["spm_cnt"] = mAdSpmStr
        map["scm_cnt"] = mAdScmStr

        QtTrackAgent.onEventObject(this, "action_sub_module_click", map, "goods_detail_page");
    }

    fun trackAdSubModuleClickWithShareView(type:String){
        var mShareIconSpmStr = "";
        var mShareIconScmStr = "";
        if (type == "2") {
            mShareIconSpmStr = "4_1." + "productDetail_" + goodsId+ "-0_0" + ".ftProdDetail@Z." + "ftFloatShare@Z_btn@2." + spmE;
            mShareIconScmStr = "appFE." + "0." + "all_0." + "text-微信好友." + scmE;
        }else{
            mShareIconSpmStr = "4_1." + "productDetail_" + goodsId + "-0_0" + ".ftProdDetail@Z." + "ftFloatShare@Z_btn@3." + spmE;
            mShareIconScmStr = "appFE." + "0." + "all_0." + "text-微信朋友圈." + scmE;
        }

        val map = HashMap<String, Any>()
        map["spm_cnt"] = mShareIconSpmStr
        map["scm_cnt"] = mShareIconScmStr

        QtTrackAgent.onEventObject(this, "action_sub_module_click", map, "goods_detail_page");
    }

}